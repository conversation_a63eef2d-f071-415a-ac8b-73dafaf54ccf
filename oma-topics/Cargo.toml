[package]
name = "oma-topics"
version = "0.18.2"
edition = "2021"
description = "AOSC OS topic (testing) repository manager used by oma"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
reqwest = { version = "0.12", features = ["json"], default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.28", default-features = false, features = ["fs"] }
thiserror = "2.0"
futures = "0.3"
tracing = "0.1"
url = "2.5"
oma-mirror = { version = "0.2.0", path = "../oma-mirror" }

[dev-dependencies]
oma-inquire = "0.1"
oma-console = { path = "../oma-console" }
tokio = { version = "1.28", default-features = false, features = ["macros", "rt-multi-thread"] }

[features]
rustls = ["reqwest/rustls-tls"]
native-tls = ["reqwest/native-tls"]
default = ["rustls"]
