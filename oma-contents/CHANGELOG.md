# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v0.1.1 (2023-08-17)

### Chore

 - <csr-id-ec5efc683e4e31ccc4688d72a4c9e7fb52e59612/> Fill in comments

### Commit Statistics

<csr-read-only-do-not-edit/>

 - 1 commit contributed to the release.
 - 1 commit was understood as [conventional](https://www.conventionalcommits.org).
 - 0 issues like '(#ID)' were seen in commit messages

### Commit Details

<csr-read-only-do-not-edit/>

<details><summary>view details</summary>

 * **Uncategorized**
    - Fill in comments ([`ec5efc6`](https://github.com/AOSC-Dev/oma/commit/ec5efc683e4e31ccc4688d72a4c9e7fb52e59612))
</details>

## v0.1.0 (2023-08-17)

<csr-id-be5d590fe8b72406216e6f8d5d7b3d21c20b0812/>
<csr-id-30a708a8419dd4d07d833a56466dffb7f290fda8/>
<csr-id-138888c4c1ebe3c79dde1493c787039a21b418f8/>
<csr-id-bdb3ee11da1a1b1149d852adfb4bad84c20f7922/>
<csr-id-898a17812a0a9d7cfe72768a491e7d0999917764/>
<csr-id-ced5b0533ac32c7473ef95d4dac1d4d9f7744ebe/>
<csr-id-28aab97554694d122e41d963443c3af96d3d28c6/>
<csr-id-8f7dd76cb91aa4e5c95c8536f663508100e79b85/>
<csr-id-99dca37ad10c2c06731fcfb33563d5aaca5b0b4b/>

### Chore

 - <csr-id-be5d590fe8b72406216e6f8d5d7b3d21c20b0812/> add desc and license (MIT)

### Chore

 - <csr-id-99dca37ad10c2c06731fcfb33563d5aaca5b0b4b/> set oma-console version as 0.1.0

### Chore

 - <csr-id-8f7dd76cb91aa4e5c95c8536f663508100e79b85/> add changelog

### New Features

 - <csr-id-4a6ebeebd4739b0f9f51fca7699b00ac18bf54d3/> Adapt oma-refresh changes

### Bug Fixes

 - <csr-id-0a0fca5caa6799ef30cde4092bad3671ce3fd506/> Fix space in file
 - <csr-id-abc85d9598597f4664ef1c600064bebf5d6eae79/> Wrong Querymode match
 - <csr-id-a748a92e7b195f73e8f1175916eb089f16f886fb/> No result retutn error

### Refactor

 - <csr-id-30a708a8419dd4d07d833a56466dffb7f290fda8/> re-abstract code
 - <csr-id-138888c4c1ebe3c79dde1493c787039a21b418f8/> lint code
 - <csr-id-bdb3ee11da1a1b1149d852adfb4bad84c20f7922/> oma provides is back
 - <csr-id-898a17812a0a9d7cfe72768a491e7d0999917764/> redesign api
 - <csr-id-ced5b0533ac32c7473ef95d4dac1d4d9f7744ebe/> done for contents.rs to oma-contents crate

### Style

 - <csr-id-28aab97554694d122e41d963443c3af96d3d28c6/> use cargo-fmt to format code

### Commit Statistics

<csr-read-only-do-not-edit/>

 - 18 commits contributed to the release over the course of 10 calendar days.
 - 13 commits were understood as [conventional](https://www.conventionalcommits.org).
 - 0 issues like '(#ID)' were seen in commit messages

### Commit Details

<csr-read-only-do-not-edit/>

<details><summary>view details</summary>

 * **Uncategorized**
    - Release oma-contents v0.1.0 ([`fc7ab38`](https://github.com/AOSC-Dev/oma/commit/fc7ab381ca0232cfec3701a57fe56cb772d690a4))
    - Set oma-console version as 0.1.0 ([`99dca37`](https://github.com/AOSC-Dev/oma/commit/99dca37ad10c2c06731fcfb33563d5aaca5b0b4b))
    - Release oma-contents v0.1.0 ([`22f6bca`](https://github.com/AOSC-Dev/oma/commit/22f6bcad8d1fab81af09a2fc1bebe4363f2f4b10))
    - Add changelog ([`8f7dd76`](https://github.com/AOSC-Dev/oma/commit/8f7dd76cb91aa4e5c95c8536f663508100e79b85))
    - Add desc and license (MIT) ([`be5d590`](https://github.com/AOSC-Dev/oma/commit/be5d590fe8b72406216e6f8d5d7b3d21c20b0812))
    - Release oma-console v0.1.0 ([`e2db19e`](https://github.com/AOSC-Dev/oma/commit/e2db19e18c756b6f4bef2a43336b3f120bce025a))
    - Re-abstract code ([`30a708a`](https://github.com/AOSC-Dev/oma/commit/30a708a8419dd4d07d833a56466dffb7f290fda8))
    - Adapt oma-refresh changes ([`4a6ebee`](https://github.com/AOSC-Dev/oma/commit/4a6ebeebd4739b0f9f51fca7699b00ac18bf54d3))
    - Lint code ([`138888c`](https://github.com/AOSC-Dev/oma/commit/138888c4c1ebe3c79dde1493c787039a21b418f8))
    - Fix space in file ([`0a0fca5`](https://github.com/AOSC-Dev/oma/commit/0a0fca5caa6799ef30cde4092bad3671ce3fd506))
    - Cargo fmt ([`799ed2d`](https://github.com/AOSC-Dev/oma/commit/799ed2d722618792689c04292a0f42770d8f5cb2))
    - Oma provides is back ([`bdb3ee1`](https://github.com/AOSC-Dev/oma/commit/bdb3ee11da1a1b1149d852adfb4bad84c20f7922))
    - Wrong Querymode match ([`abc85d9`](https://github.com/AOSC-Dev/oma/commit/abc85d9598597f4664ef1c600064bebf5d6eae79))
    - Redesign api ([`898a178`](https://github.com/AOSC-Dev/oma/commit/898a17812a0a9d7cfe72768a491e7d0999917764))
    - No result retutn error ([`a748a92`](https://github.com/AOSC-Dev/oma/commit/a748a92e7b195f73e8f1175916eb089f16f886fb))
    - Use cargo-fmt to format code ([`28aab97`](https://github.com/AOSC-Dev/oma/commit/28aab97554694d122e41d963443c3af96d3d28c6))
    - Done for contents.rs to oma-contents crate ([`ced5b05`](https://github.com/AOSC-Dev/oma/commit/ced5b0533ac32c7473ef95d4dac1d4d9f7744ebe))
    - Init ([`49ba1f3`](https://github.com/AOSC-Dev/oma/commit/49ba1f381b98f785f5e8721a404ec1c596a54a63))
</details>
