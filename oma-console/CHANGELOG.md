# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v0.1.2 (2023-09-05)

### Bug Fixes

 - <csr-id-5b3e7018d238a50e9ac1e203cdfdfb76aec051b0/> I18n message in progress bar display

### Commit Statistics

<csr-read-only-do-not-edit/>

 - 1 commit contributed to the release.
 - 9 days passed between releases.
 - 1 commit was understood as [conventional](https://www.conventionalcommits.org).
 - 0 issues like '(#ID)' were seen in commit messages

### Commit Details

<csr-read-only-do-not-edit/>

<details><summary>view details</summary>

 * **Uncategorized**
    - I18n message in progress bar display ([`5b3e701`](https://github.com/AOSC-Dev/oma/commit/5b3e7018d238a50e9ac1e203cdfdfb76aec051b0))
</details>

## v0.1.1 (2023-08-26)

<csr-id-ec081f1040db85091a7cc12f1b0db2fde4fe39bc/>
<csr-id-d8f9c63926040c9595ce84d592d9efe611e0491f/>
<csr-id-25d16028ab2d73a160e2b071957186b3a1b30758/>
<csr-id-a74fe0745663ea6d25249ff376bcaac9afda7393/>
<csr-id-0ca5be73a7ddb70e3a07b63ef21f2f873e420832/>
<csr-id-336b02cd7f1e950d028724c11d2318bed0495ddc/>
<csr-id-24ca3e6751a08cf5fcbbe0aa9c84d0ae4fc7de6b/>
<csr-id-b8fc1a95ccb112e3f0be406f3ab7c6b70fcfefef/>
<csr-id-fbebfb815cdad214a609e9cf594648aaf7ee6b6a/>
<csr-id-87ebc18929eb44c4b96f0cc9a30822a5277ff440/>
<csr-id-f310ff2486eaba37b8e659991429a81dfea4dff7/>
<csr-id-df5692d9cd2dea3e882205dcce6d0558b539e279/>
<csr-id-7de1d4f70f474a97d551cdbecc0c3e988f2544be/>
<csr-id-a0ba9b4f999eaf047ecc79312a36237c8c70d4d3/>
<csr-id-9de51fa2cf2993c10acfd05d3cda133e6140ac44/>

### Chore

 - <csr-id-ec081f1040db85091a7cc12f1b0db2fde4fe39bc/> Use MIT license
 - <csr-id-d8f9c63926040c9595ce84d592d9efe611e0491f/> Add desc
 - <csr-id-25d16028ab2d73a160e2b071957186b3a1b30758/> Add changelog
 - <csr-id-a74fe0745663ea6d25249ff376bcaac9afda7393/> Fill of comment
 - <csr-id-0ca5be73a7ddb70e3a07b63ef21f2f873e420832/> No need to use tracing

### New Features

 - <csr-id-996417b5a659b404729d79522d02d11561b0375d/> Display done message if env is not atty
 - <csr-id-8d37b9d590b8085b6bbca9798a70286cc2c6816b/> Use DEBUG globar var to store yes/no display debug message
 - <csr-id-1d9b5d15dc76e74672be7b0d610202ad1dc11fdb/> Fill of remove() function
 - <csr-id-5cedd38dc69b89403b8f13aa8b68a6360481991b/> Init

### Bug Fixes

 - <csr-id-94239ec928737b94072cad2a399892e98e33f54e/> Fix logger marco multi use

### Refactor

 - <csr-id-336b02cd7f1e950d028724c11d2318bed0495ddc/> Remove useless file; lint
 - <csr-id-24ca3e6751a08cf5fcbbe0aa9c84d0ae4fc7de6b/> Fill of error translate (50%)
 - <csr-id-b8fc1a95ccb112e3f0be406f3ab7c6b70fcfefef/> Improve debug marco
 - <csr-id-fbebfb815cdad214a609e9cf594648aaf7ee6b6a/> Add progressbar style
   - Also add oma-fetcher ProgressStyle
 - <csr-id-87ebc18929eb44c4b96f0cc9a30822a5277ff440/> Add todo
 - <csr-id-f310ff2486eaba37b8e659991429a81dfea4dff7/> Do not const Writer::default as WRITER
 - <csr-id-df5692d9cd2dea3e882205dcce6d0558b539e279/> Add oma-topics crate
 - <csr-id-7de1d4f70f474a97d551cdbecc0c3e988f2544be/> Abstract tips and has_x11
 - <csr-id-a0ba9b4f999eaf047ecc79312a36237c8c70d4d3/> Add crate oma-console

### Style

 - <csr-id-9de51fa2cf2993c10acfd05d3cda133e6140ac44/> Run cargo clippy and cargo fmt to lint code

### Commit Statistics

<csr-read-only-do-not-edit/>

 - 23 commits contributed to the release over the course of 4 calendar days.
 - 20 commits were understood as [conventional](https://www.conventionalcommits.org).
 - 0 issues like '(#ID)' were seen in commit messages

### Commit Details

<csr-read-only-do-not-edit/>

<details><summary>view details</summary>

 * **Uncategorized**
    - Bump oma-console v0.1.1, oma-fetch v0.1.2, oma-utils v0.1.4, oma-pm v0.2.1 ([`64f5d1b`](https://github.com/AOSC-Dev/oma/commit/64f5d1bf4f93b7b3b1f5a00134e232409458e5e3))
    - Release oma-console v0.1.0 ([`d9b8f96`](https://github.com/AOSC-Dev/oma/commit/d9b8f963c7db7d074c6f3c09e5547772bdaff396))
    - Use MIT license ([`ec081f1`](https://github.com/AOSC-Dev/oma/commit/ec081f1040db85091a7cc12f1b0db2fde4fe39bc))
    - Release oma-console v0.1.0 ([`e36660d`](https://github.com/AOSC-Dev/oma/commit/e36660d6429addd513195b926bc3825f73f51863))
    - Add desc ([`d8f9c63`](https://github.com/AOSC-Dev/oma/commit/d8f9c63926040c9595ce84d592d9efe611e0491f))
    - Add changelog ([`25d1602`](https://github.com/AOSC-Dev/oma/commit/25d16028ab2d73a160e2b071957186b3a1b30758))
    - Fill of comment ([`a74fe07`](https://github.com/AOSC-Dev/oma/commit/a74fe0745663ea6d25249ff376bcaac9afda7393))
    - Display done message if env is not atty ([`996417b`](https://github.com/AOSC-Dev/oma/commit/996417b5a659b404729d79522d02d11561b0375d))
    - Remove useless file; lint ([`336b02c`](https://github.com/AOSC-Dev/oma/commit/336b02cd7f1e950d028724c11d2318bed0495ddc))
    - Fill of error translate (50%) ([`24ca3e6`](https://github.com/AOSC-Dev/oma/commit/24ca3e6751a08cf5fcbbe0aa9c84d0ae4fc7de6b))
    - No need to use tracing ([`0ca5be7`](https://github.com/AOSC-Dev/oma/commit/0ca5be73a7ddb70e3a07b63ef21f2f873e420832))
    - Use DEBUG globar var to store yes/no display debug message ([`8d37b9d`](https://github.com/AOSC-Dev/oma/commit/8d37b9d590b8085b6bbca9798a70286cc2c6816b))
    - Fix logger marco multi use ([`94239ec`](https://github.com/AOSC-Dev/oma/commit/94239ec928737b94072cad2a399892e98e33f54e))
    - Improve debug marco ([`b8fc1a9`](https://github.com/AOSC-Dev/oma/commit/b8fc1a95ccb112e3f0be406f3ab7c6b70fcfefef))
    - Fill of remove() function ([`1d9b5d1`](https://github.com/AOSC-Dev/oma/commit/1d9b5d15dc76e74672be7b0d610202ad1dc11fdb))
    - Run cargo clippy and cargo fmt to lint code ([`9de51fa`](https://github.com/AOSC-Dev/oma/commit/9de51fa2cf2993c10acfd05d3cda133e6140ac44))
    - Init ([`5cedd38`](https://github.com/AOSC-Dev/oma/commit/5cedd38dc69b89403b8f13aa8b68a6360481991b))
    - Add progressbar style ([`fbebfb8`](https://github.com/AOSC-Dev/oma/commit/fbebfb815cdad214a609e9cf594648aaf7ee6b6a))
    - Add todo ([`87ebc18`](https://github.com/AOSC-Dev/oma/commit/87ebc18929eb44c4b96f0cc9a30822a5277ff440))
    - Do not const Writer::default as WRITER ([`f310ff2`](https://github.com/AOSC-Dev/oma/commit/f310ff2486eaba37b8e659991429a81dfea4dff7))
    - Add oma-topics crate ([`df5692d`](https://github.com/AOSC-Dev/oma/commit/df5692d9cd2dea3e882205dcce6d0558b539e279))
    - Abstract tips and has_x11 ([`7de1d4f`](https://github.com/AOSC-Dev/oma/commit/7de1d4f70f474a97d551cdbecc0c3e988f2544be))
    - Add crate oma-console ([`a0ba9b4`](https://github.com/AOSC-Dev/oma/commit/a0ba9b4f999eaf047ecc79312a36237c8c70d4d3))
</details>

## v0.1.0 (2023-08-17)

<csr-id-3c7c29e6e5da4cc1b4e10006aa9cac2b2008d43a/>
<csr-id-eb52b648a8b51af5bdf1cd39dd3045c49267f399/>
<csr-id-119cc9f79cb3e0a2c1e5623614915c6e7c0b8769/>
<csr-id-9e6f244eaf4e52c13107c2dc6b42432982b5eb37/>
<csr-id-999ff58a1a4d6d5ceecb8563018a21b0002c90ae/>
<csr-id-61b0cf19043ce4ee0a50fa2ee1584248a03d30bf/>
<csr-id-b210c488cd00656131cf77ef7f98a5aef0999e73/>
<csr-id-3f0e43a4b964113b261b5688b612c71f6c87b7b1/>
<csr-id-5170f79611bb4b36baa7a179167de5cd3b141a2e/>
<csr-id-255c03b6d49e612578cd75f7e8c92aba273a2308/>
<csr-id-7dba4beee8cf26d469b01dbbe5b61b06d73622b2/>
<csr-id-ee45498f402ccc6a686c44b1b4f887301e9801e1/>
<csr-id-ec5d5d35534f5200143f6d819ca5d2ed989fd21c/>
<csr-id-294cd1b853c63d48ab1fcb33db95ea3838ab47dd/>
<csr-id-548a9e5febf471f0e62d0f1202d9465d493a889f/>

### Chore

 - <csr-id-3c7c29e6e5da4cc1b4e10006aa9cac2b2008d43a/> fill of comment
 - <csr-id-eb52b648a8b51af5bdf1cd39dd3045c49267f399/> no need to use tracing

### Chore

 - <csr-id-548a9e5febf471f0e62d0f1202d9465d493a889f/> use MIT license

### Chore

 - <csr-id-ec5d5d35534f5200143f6d819ca5d2ed989fd21c/> add desc
 - <csr-id-294cd1b853c63d48ab1fcb33db95ea3838ab47dd/> add changelog

### New Features

 - <csr-id-e8f56b5f1634556fd269d2b598d37f12eb1dfab7/> Display done message if env is not atty
 - <csr-id-0a5a509cdd4d46e1848bbfae989f3dc752bf7e80/> Use DEBUG globar var to store yes/no display debug message
 - <csr-id-e92a420653a852ebd2d26d2cbf91dd2f7cded154/> Fill of remove() function
 - <csr-id-b50cfc0a5337053c496876de84eaf00f221884ed/> Init

### Bug Fixes

 - <csr-id-2037757c6ebde5a94f85f4b1802674ac3c10d05f/> Fix logger marco multi use

### Refactor

 - <csr-id-119cc9f79cb3e0a2c1e5623614915c6e7c0b8769/> remove useless file; lint
 - <csr-id-9e6f244eaf4e52c13107c2dc6b42432982b5eb37/> fill of error translate (50%)
 - <csr-id-999ff58a1a4d6d5ceecb8563018a21b0002c90ae/> improve debug marco
 - <csr-id-61b0cf19043ce4ee0a50fa2ee1584248a03d30bf/> add progressbar style
   - Also add oma-fetcher ProgressStyle
 - <csr-id-b210c488cd00656131cf77ef7f98a5aef0999e73/> add todo
 - <csr-id-3f0e43a4b964113b261b5688b612c71f6c87b7b1/> do not const Writer::default as WRITER
 - <csr-id-5170f79611bb4b36baa7a179167de5cd3b141a2e/> add oma-topics crate
 - <csr-id-255c03b6d49e612578cd75f7e8c92aba273a2308/> abstract tips and has_x11
 - <csr-id-7dba4beee8cf26d469b01dbbe5b61b06d73622b2/> add crate oma-console

### Style

 - <csr-id-ee45498f402ccc6a686c44b1b4f887301e9801e1/> run cargo clippy and cargo fmt to lint code
