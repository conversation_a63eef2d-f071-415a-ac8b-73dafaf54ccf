[package]
name = "oma-console"
version = "0.23.0"
edition = "2021"
description = "Console and terminal emulator handling library used by oma"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
console = { version = "0.15", optional = true }
indicatif = { version = "0.17", optional = true }
tracing = { version = "0.1", optional = true }
tracing-subscriber =  { version = "0.3", optional = true }
ratatui = { version = "0.29", optional = true }
# https://github.com/async-rs/async-std/issues/1055
# https://github.com/tokio-rs/tokio/issues/5535
termbg = { version = "0.1", package = "termbg-with-async-stdin", optional = true }
crossterm = {version = "0.28", optional = true}
ansi-to-tui = { version = "7.0", optional = true }
textwrap = { version = "0.16", optional = true }

[features]
print = ["dep:tracing", "dep:tracing-subscriber", "dep:textwrap", "dep:console", "dep:termbg"]
pager = ["dep:ratatui", "dep:crossterm", "dep:ansi-to-tui", "dep:console"]
progress_bar_style = ["dep:indicatif"]
default = ["print", "pager", "progress_bar_style"]
