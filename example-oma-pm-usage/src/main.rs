use std::sync::atomic::Ordering;
use anyhow::Result;
use apt_auth_config::AuthConfig;
use dashmap::DashMap;
use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use oma_fetch::{reqwest::ClientBuilder, DownloadProgressControl};
use oma_pm::{
    apt::{AptConfig, CommitDownloadConfig, OmaApt, OmaAptArgs, SummarySort},
    matches::PackagesMatcher,
    progress::InstallProgressManager,
};
use oma_utils::dpkg::dpkg_arch;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 检查是否以 root 权限运行
    if !nix::unistd::geteuid().is_root() {
        warn!("This program should be run as root for package installation");
        return Ok(());
    }

    let package_manager = PackageManager::new()?;
    
    // 示例：安装软件包
    package_manager.install_packages(&["htop", "curl"]).await?;
    
    // 示例：搜索软件包
    let search_results = package_manager.search_packages("vim")?;
    info!("Found {} packages matching 'vim'", search_results.len());
    
    // 示例：检查可升级的软件包
    let upgradable_count = package_manager.count_upgradable_packages()?;
    info!("Found {} upgradable packages", upgradable_count);

    Ok(())
}

pub struct PackageManager {
    apt: OmaApt,
    client: reqwest::Client,
    arch: String,
}

impl PackageManager {
    pub fn new() -> Result<Self> {
        let oma_apt_args = OmaAptArgs::builder()
            .yes(true)  // 自动确认操作
            .sysroot("/".to_string())
            .install_recommends(true)
            .build();

        let apt = OmaApt::new(
            vec![],  // 没有本地 deb 文件
            oma_apt_args,
            false,   // 不是 dry run
            AptConfig::new(),
        )?;

        let client = ClientBuilder::new()
            .user_agent("example-oma-pm-usage/0.1.0")
            .build()?;

        let arch = dpkg_arch("/")?;

        Ok(Self { apt, client, arch })
    }

    pub async fn install_packages(&self, package_names: &[&str]) -> Result<()> {
        info!("Installing packages: {:?}", package_names);

        let matcher = PackagesMatcher::builder()
            .cache(&self.apt.cache)
            .filter_candidate(true)
            .filter_downloadable_candidate(false)
            .select_dbg(false)
            .native_arch(&self.arch)
            .build();

        // 搜索软件包
        let (pkgs, no_result) = matcher.match_pkgs_and_versions(package_names.iter().copied())?;
        
        if !no_result.is_empty() {
            warn!("Could not find packages: {:?}", no_result);
        }

        if pkgs.is_empty() {
            warn!("No packages to install");
            return Ok(());
        }

        // 创建可变的 apt 实例用于安装
        let mut apt = OmaApt::new(
            vec![],
            OmaAptArgs::builder().yes(true).build(),
            false,
            AptConfig::new(),
        )?;

        // 标记要安装的软件包
        let no_marked_install = apt.install(&pkgs, false)?;
        
        if !no_marked_install.is_empty() {
            info!("Already installed: {:?}", no_marked_install);
        }

        // 解析依赖关系
        apt.resolve(false, false)?;

        // 生成操作摘要
        let op = apt.summary(SummarySort::Operation, |_| false, |_| false)?;
        
        info!("Operation summary: {} packages to install", op.install.len());

        // 创建进度管理器
        let download_pm = MyDownloadProgressManager::default();
        let install_pm = Box::new(MyInstallProgressManager);

        // 执行安装
        apt.commit(
            &self.client,
            CommitDownloadConfig {
                network_thread: None,
                auth: &AuthConfig::system("/")?,
            },
            &download_pm,
            install_pm,
            op,
        )?;

        info!("Installation completed successfully");
        Ok(())
    }

    pub fn search_packages(&self, query: &str) -> Result<Vec<String>> {
        let matcher = PackagesMatcher::builder()
            .cache(&self.apt.cache)
            .filter_candidate(true)
            .filter_downloadable_candidate(false)
            .select_dbg(false)
            .native_arch(&self.arch)
            .build();

        let pattern = format!("*{}*", query);
        let (pkgs, _) = matcher.match_pkgs_and_versions([pattern.as_str()])?;
        
        let results = pkgs.iter()
            .map(|pkg| pkg.raw_pkg.fullname(true))
            .collect();

        Ok(results)
    }

    pub fn count_upgradable_packages(&self) -> Result<usize> {
        let count = self.apt.count_pending_upgradable_pkgs()?;
        Ok(count)
    }
}

// 下载进度管理器
#[derive(Default)]
struct MyDownloadProgressManager {
    mb: MultiProgress,
    pb_map: DashMap<usize, ProgressBar>,
}

impl DownloadProgressControl for MyDownloadProgressManager {
    fn checksum_mismatch_retry(&self, _index: usize, filename: &str, times: usize) {
        self.mb
            .println(format!("{filename} checksum failed, retrying {times} times"))
            .unwrap();
    }

    fn global_progress_set(&self, num: &std::sync::atomic::AtomicU64) {
        if let Some(pb) = self.pb_map.get(&0) {
            pb.set_position(num.load(Ordering::SeqCst));
        }
    }

    fn progress_done(&self, index: usize) {
        if let Some(pb) = self.pb_map.get(&(index + 1)) {
            pb.finish_and_clear();
        }
    }

    fn new_progress_spinner(&self, index: usize, msg: &str) {
        let style = ProgressStyle::default_spinner()
            .template("{spinner:.green} {msg}")
            .unwrap();
        let pb = self.mb.insert(index + 1, ProgressBar::new_spinner().with_style(style));
        pb.set_message(msg.to_string());
        pb.enable_steady_tick(std::time::Duration::from_millis(100));
        self.pb_map.insert(index + 1, pb);
    }

    fn new_progress_bar(&self, index: usize, msg: &str, size: u64) {
        let style = ProgressStyle::default_bar()
            .template("{msg} [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
            .unwrap();
        let pb = self.mb.insert(index + 1, ProgressBar::new(size).with_style(style));
        pb.set_message(msg.to_string());
        self.pb_map.insert(index + 1, pb);
    }

    fn progress_inc(&self, index: usize, num: u64) {
        if let Some(pb) = self.pb_map.get(&(index + 1)) {
            pb.inc(num);
        }
    }

    fn progress_set(&self, index: usize, num: u64) {
        if let Some(pb) = self.pb_map.get(&(index + 1)) {
            pb.set_position(num);
        }
    }

    fn failed_to_get_source_next_url(&self, _index: usize, err: &str) {
        self.mb.println(format!("Error: {err}")).unwrap();
    }

    fn download_done(&self, _index: usize, _msg: &str) {}

    fn all_done(&self) {}

    fn new_global_progress_bar(&self, total_size: u64) {
        let style = ProgressStyle::default_bar()
            .template("Total [{bar:40.cyan/blue}] {bytes}/{total_bytes}")
            .unwrap();
        let pb = self.mb.insert(0, ProgressBar::new(total_size).with_style(style));
        self.pb_map.insert(0, pb);
    }
}

// 安装进度管理器
struct MyInstallProgressManager;

impl InstallProgressManager for MyInstallProgressManager {
    fn status_change(&self, pkgname: &str, steps_done: u64, total_steps: u64, _config: &AptConfig) {
        let percent = (steps_done as f32 / total_steps as f32 * 100.0) as u32;
        info!("Installing {}: {}% ({}/{})", pkgname, percent, steps_done, total_steps);
    }

    fn no_interactive(&self) -> bool {
        true  // 非交互模式
    }

    fn use_pty(&self) -> bool {
        false  // 不使用 PTY
    }
}
