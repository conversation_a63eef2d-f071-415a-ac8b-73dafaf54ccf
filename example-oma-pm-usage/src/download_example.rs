use std::path::Path;
use anyhow::Result;
use apt_auth_config::AuthConfig;
use oma_fetch::reqwest::ClientBuilder;
use oma_pm::{
    apt::{AptConfig, DownloadConfig, OmaApt, OmaAptArgs},
    matches::PackagesMatcher,
};
use oma_utils::dpkg::dpkg_arch;
use tracing::info;

/// 简单的下载示例 - 只下载软件包而不安装
pub async fn download_packages_example() -> Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("Starting package download example");

    // 初始化 APT 管理器
    let oma_apt_args = OmaAptArgs::builder().build();
    let apt = OmaApt::new(vec![], oma_apt_args, false, AptConfig::new())?;
    let arch = dpkg_arch("/")?;

    // 创建包匹配器
    let matcher = PackagesMatcher::builder()
        .cache(&apt.cache)
        .filter_candidate(true)
        .filter_downloadable_candidate(false)
        .select_dbg(false)
        .native_arch(&arch)
        .build();

    // 搜索要下载的软件包
    let package_names = ["curl", "wget"];
    let (pkgs, no_result) = matcher.match_pkgs_and_versions(package_names.iter().copied())?;

    if !no_result.is_empty() {
        info!("Could not find packages: {:?}", no_result);
    }

    if pkgs.is_empty() {
        info!("No packages found to download");
        return Ok(());
    }

    info!("Found {} packages to download", pkgs.len());
    for pkg in &pkgs {
        info!("  - {} {}", pkg.raw_pkg.fullname(true), pkg.version_raw.version());
    }

    // 创建下载目录
    let download_dir = Path::new("./downloads");
    std::fs::create_dir_all(download_dir)?;

    // 创建 HTTP 客户端
    let client = ClientBuilder::new()
        .user_agent("oma-pm-download-example/0.1.0")
        .build()?;

    // 简单的进度管理器（只打印消息）
    let progress_manager = SimpleProgressManager;

    // 下载软件包
    let download_config = DownloadConfig {
        network_thread: None,
        download_dir: Some(download_dir),
        auth: &AuthConfig::system("/")?,
    };

    let (summaries, errors) = apt.download(
        &client,
        pkgs,
        download_config,
        false, // 不是 dry run
        &progress_manager,
    )?;

    // 报告结果
    info!("Download completed!");
    info!("Successfully downloaded {} packages", summaries.len());
    
    for summary in &summaries {
        info!("  ✓ Downloaded: {}", summary.filename);
    }

    if !errors.is_empty() {
        info!("Encountered {} errors:", errors.len());
        for error in &errors {
            info!("  ✗ Error: {:?}", error);
        }
    }

    Ok(())
}

// 简单的进度管理器实现
struct SimpleProgressManager;

impl oma_fetch::DownloadProgressControl for SimpleProgressManager {
    fn checksum_mismatch_retry(&self, _index: usize, filename: &str, times: usize) {
        info!("Checksum mismatch for {}, retrying {} times", filename, times);
    }

    fn global_progress_set(&self, _num: &std::sync::atomic::AtomicU64) {
        // 可以在这里更新全局进度
    }

    fn progress_done(&self, index: usize) {
        info!("Download {} completed", index);
    }

    fn new_progress_spinner(&self, index: usize, msg: &str) {
        info!("Starting download {}: {}", index, msg);
    }

    fn new_progress_bar(&self, index: usize, msg: &str, size: u64) {
        info!("Starting download {}: {} ({} bytes)", index, msg, size);
    }

    fn progress_inc(&self, _index: usize, _num: u64) {
        // 可以在这里更新进度
    }

    fn progress_set(&self, _index: usize, _num: u64) {
        // 可以在这里设置进度
    }

    fn failed_to_get_source_next_url(&self, _index: usize, err: &str) {
        info!("Failed to get source URL: {}", err);
    }

    fn download_done(&self, _index: usize, msg: &str) {
        info!("Download completed: {}", msg);
    }

    fn all_done(&self) {
        info!("All downloads completed!");
    }

    fn new_global_progress_bar(&self, total_size: u64) {
        info!("Starting downloads, total size: {} bytes", total_size);
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    download_packages_example().await
}
