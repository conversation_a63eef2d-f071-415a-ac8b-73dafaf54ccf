[package]
name = "example-oma-pm-usage"
version = "0.1.0"
edition = "2021"

[dependencies]
# 核心包管理库
oma-pm = "0.42.3"
oma-utils = { version = "0.10.2", features = ["dpkg", "human-bytes"] }
oma-fetch = "0.19.0"
apt-auth-config = "0.2.0"

# 必需的依赖
tokio = { version = "1.15", features = ["rt-multi-thread", "fs"] }
tracing = "0.1"
tracing-subscriber = "0.3"
anyhow = "1.0"

# 进度条和用户界面
indicatif = "0.17"
dashmap = "6"

# 可选：更好的错误处理
thiserror = "2"
