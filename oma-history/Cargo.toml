[package]
name = "oma-history"
version = "0.4.10"
edition = "2021"
description = "Package manager operations history database management library"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
oma-pm-operation-type = { version = "0.7", path = "../oma-pm-operation-type" }
rusqlite = { version = "0.32", features = ["bundled"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
thiserror = "2"
