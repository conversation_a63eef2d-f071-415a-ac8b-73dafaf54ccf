[package]
name = "oma-pm"
version = "0.42.3"
edition = "2021"
description = "APT package manager API abstraction library"
license = "GPL-3.0-or-later"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
oma-apt = "0.8"
thiserror = "2"
chrono = { version = "0.4", features = ["unstable-locales"] }
glob-match = "0.2"
indicium = "0.6"
oma-utils = { version = "^0.10.0", path = "../oma-utils", features = [
    "dpkg",
    "url-no-escape",
    "human-bytes",
] }
oma-fetch = { version = "^0.19.0", path = "../oma-fetch" }
tokio = { version = "1.15", default-features = false, features = [
    "fs",
    "rt-multi-thread",
] }
oma-console = { version = "^0.23.0", path = "../oma-console", default-features = false, features = [
    "print",
] }
fs4 = "0.11"
tracing = "0.1"
oma-pm-operation-type = { version = "0.7", path = "../oma-pm-operation-type" }
zbus = { version = "4.1", features = ["tokio"] }
cxx = "1.0.121"
ahash = "0.8.11"
bon = "3"
strsim = "0.11.1"
indexmap = "2"
memchr = "2"
serde = { version = "1", features = ["derive"] }
apt-auth-config = { version = "0.2.0", path = "../apt-auth-config" }

[dev-dependencies]
dashmap = "6"
indicatif = "0.17"
oma-console = { version = "^0.23.0", path = "../oma-console", default-features = false, features = ["progress_bar_style"] }

[features]
aosc = []
