Package: qaq
Version: 9999:1
Section: devel
Architecture: all
Installed-Size: 20
Maintainer: <PERSON><PERSON><PERSON> <jeff<PERSON><PERSON>@aosc.io>
Filename: pool/stable/main/d/devel-base_1-0_noarch.deb
Size: 1008
SHA256: 2b9844eaea74fba32b3f1db72786eea7e42a6c31c46bcb5765142990a5b590db
Description: Meta package for AOSC OS development/packaging support
Breaks: pap (<= 0-3)
Depends: binutils, bison, diffutils, flex, gawk, gcc, groff, libtool, m4, make, patch, pkg-config, sed, autoconf, automake, cmake, gettext, help2man, texinfo, meson, ninja
Provides: windows-nt-kernel, pwp
Replaces: pap (<= 0-3)

Package: qwq-desktop
Version: 9999:114514
Section: web
Architecture: all
Installed-Size: 89144
Maintainer: <PERSON> <<EMAIL>>
Filename: pool/stable/main/t/telegram-desktop_4.12.2-1_amd64.deb
Size: 31559728
SHA256: 48e43c88e889124fae44d0dfcf7d84ab699cba64c781e001889bb19f005f6c8e
Description: The official Telegram desktop application
Provides: qwq, qwqdesktop
Recommends: kimageformats
Depends: ffmpeg (>= 4.2.5-5), hicolor-icon-theme (>= 0.17-2), libnotify (>= 0.7.9-1), minizip (>= 1.2.11-2), abseil-cpp (>= 20220623.1), openal-soft (>= 1.23.1), openssl (>= 3.1.4), lz4 (>= 1:1.9.4), qt-5 (>= 1:5.15.5+webengine5.15.10+webkit5.212.0+kde20220705-5), xxhash (>= 0.8.1), hunspell (>= 1.7.0-4), libdispatch (>= 5.6.1-1), libjpeg-turbo (>= 2:2.1.4), opus (>= 1.3.1), pulseaudio (>= 13.0-10), rnnoise (>= 1:0+git20210312), pipewire (>= 0.3.71-1), kcoreaddons (>= 5.103.0), glibmm-2.68 (>= 2.78.0), fmt (>= 8.0.1-1)
Suggests: webkit2gtk

Package: owo
Version: 9999:2.6.1-2
Section: admin
Architecture: all
Installed-Size: 30748
Maintainer: AOSC OS Maintainers <<EMAIL>>
Filename: pool/stable/main/a/apt_2.6.1-2_amd64.deb
Size: 3433876
SHA256: 161c32c6414792378fedcdd27d5feb3bca818ff9eaad46f37f0787e8d101e1a4
Description: Advanced Packaging Tools
Depends: gnupg (>= 1:2.4.4), dpkg (>= 1.21.22), glibc (>= 1:2.37-2), zlib (>= 1.3.1), bzip2 (>= 1.0.8-5), lz4 (>= 1:1.9.4-1), systemd (>= 1:255.3-1), gnutls (>= 3.8.3), zstd (>= 1.5.5)
Recommends: mirrormgr
Breaks: acbs (<= 1:20200626), aptitude (<= 0.8.10-5), libapt-pkg-perl (<= 0.1.36-1), packagekit (<= 1.1.13-2), synaptic (<= 0.84.6)
