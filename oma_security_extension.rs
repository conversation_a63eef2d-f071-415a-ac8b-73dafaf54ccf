use anyhow::Result;
use oma_pm::apt::{OmaApt, OmaAptResult};
use oma_apt::cache::PackageSort;
use std::collections::HashMap;

/// 为 OmaApt 添加安全更新功能的扩展 trait
pub trait OmaSecurityExt {
    /// 获取安全更新数量
    fn count_security_updates(&self) -> OmaAptResult<usize>;
    
    /// 获取安全更新列表
    fn get_security_updates(&self) -> OmaAptResult<Vec<SecurityUpdateInfo>>;
    
    /// 检查是否有紧急安全更新
    fn has_critical_security_updates(&self) -> OmaAptResult<bool>;
    
    /// 获取按优先级分组的安全更新
    fn get_security_updates_by_priority(&self) -> OmaAptResult<HashMap<SecurityPriority, Vec<SecurityUpdateInfo>>>;
}

#[derive(Debug, Clone)]
pub struct SecurityUpdateInfo {
    pub package_name: String,
    pub current_version: String,
    pub new_version: String,
    pub priority: SecurityPriority,
    pub source_archive: Option<String>,
    pub is_essential: bool,
}

#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub enum SecurityPriority {
    Critical,
    High,
    Medium,
    Low,
}

impl OmaSecurityExt for OmaApt {
    fn count_security_updates(&self) -> OmaAptResult<usize> {
        let security_updates = self.get_security_updates()?;
        Ok(security_updates.len())
    }

    fn get_security_updates(&self) -> OmaAptResult<Vec<SecurityUpdateInfo>> {
        let mut security_updates = Vec::new();
        let sort = PackageSort::default().upgradable();
        
        for pkg in self.cache.packages(&sort) {
            if let (Some(installed), Some(candidate)) = (pkg.installed(), pkg.candidate()) {
                // 检查是否为安全更新
                let is_security = self.is_security_update(&pkg, &candidate);
                
                if is_security {
                    let priority = self.determine_security_priority(&pkg, &candidate);
                    let source_archive = candidate.package_files()
                        .find_map(|pf| pf.archive().map(|s| s.to_string()));
                    
                    security_updates.push(SecurityUpdateInfo {
                        package_name: pkg.fullname(true),
                        current_version: installed.version().to_string(),
                        new_version: candidate.version().to_string(),
                        priority,
                        source_archive,
                        is_essential: pkg.is_essential(),
                    });
                }
            }
        }
        
        // 按优先级排序
        security_updates.sort_by(|a, b| a.priority.cmp(&b.priority));
        
        Ok(security_updates)
    }

    fn has_critical_security_updates(&self) -> OmaAptResult<bool> {
        let security_updates = self.get_security_updates()?;
        Ok(security_updates.iter().any(|update| update.priority == SecurityPriority::Critical))
    }

    fn get_security_updates_by_priority(&self) -> OmaAptResult<HashMap<SecurityPriority, Vec<SecurityUpdateInfo>>> {
        let security_updates = self.get_security_updates()?;
        let mut grouped = HashMap::new();
        
        for update in security_updates {
            grouped.entry(update.priority.clone())
                .or_insert_with(Vec::new)
                .push(update);
        }
        
        Ok(grouped)
    }
}

impl OmaApt {
    /// 检查包更新是否为安全更新
    fn is_security_update(&self, pkg: &oma_apt::Package, candidate: &oma_apt::Version) -> bool {
        // 检查包源
        for package_file in candidate.package_files() {
            if let Some(archive) = package_file.archive() {
                let archive_lower = archive.to_lowercase();
                
                // 常见的安全更新源标识
                if archive_lower.contains("security") ||
                   archive_lower.contains("-security") ||
                   archive_lower.contains("stable-security") ||
                   archive_lower.contains("oldstable-security") {
                    return true;
                }
            }
            
            // 检查组件
            if let Some(component) = package_file.component() {
                if component.to_lowercase().contains("security") {
                    return true;
                }
            }
        }
        
        // 检查描述中的安全关键词
        if let Some(description) = candidate.description() {
            let desc_lower = description.to_lowercase();
            let security_keywords = [
                "security fix", "vulnerability", "cve-", "exploit",
                "buffer overflow", "privilege escalation", "denial of service",
                "remote code execution", "cross-site scripting", "sql injection"
            ];
            
            for keyword in &security_keywords {
                if desc_lower.contains(keyword) {
                    return true;
                }
            }
        }
        
        false
    }

    /// 确定安全更新的优先级
    fn determine_security_priority(&self, pkg: &oma_apt::Package, candidate: &oma_apt::Version) -> SecurityPriority {
        // Essential 包的安全更新优先级更高
        if pkg.is_essential() {
            return SecurityPriority::Critical;
        }
        
        // 检查描述中的严重程度指示
        if let Some(description) = candidate.description() {
            let desc_lower = description.to_lowercase();
            
            if desc_lower.contains("critical") || 
               desc_lower.contains("remote code execution") ||
               desc_lower.contains("privilege escalation") {
                return SecurityPriority::Critical;
            }
            
            if desc_lower.contains("high") ||
               desc_lower.contains("buffer overflow") ||
               desc_lower.contains("denial of service") {
                return SecurityPriority::High;
            }
            
            if desc_lower.contains("medium") ||
               desc_lower.contains("cross-site scripting") ||
               desc_lower.contains("sql injection") {
                return SecurityPriority::Medium;
            }
        }
        
        // 默认为低优先级
        SecurityPriority::Low
    }
}

/// 使用示例
#[cfg(test)]
mod tests {
    use super::*;
    use oma_pm::apt::{AptConfig, OmaAptArgs};

    #[test]
    fn test_security_updates() -> Result<()> {
        let args = OmaAptArgs::builder().build();
        let apt = OmaApt::new(vec![], args, false, AptConfig::new())?;
        
        // 测试安全更新计数
        let count = apt.count_security_updates()?;
        println!("Security updates available: {}", count);
        
        // 测试获取安全更新列表
        let updates = apt.get_security_updates()?;
        for update in &updates {
            println!("Security update: {} {} → {} ({:?})", 
                update.package_name,
                update.current_version,
                update.new_version,
                update.priority
            );
        }
        
        // 测试是否有关键安全更新
        let has_critical = apt.has_critical_security_updates()?;
        if has_critical {
            println!("⚠️  Critical security updates available!");
        }
        
        // 测试按优先级分组
        let grouped = apt.get_security_updates_by_priority()?;
        for (priority, updates) in grouped {
            println!("{:?} priority updates: {}", priority, updates.len());
        }
        
        Ok(())
    }
}

/// 完整的使用示例
pub fn example_usage() -> Result<()> {
    use oma_pm::apt::{AptConfig, OmaAptArgs};
    use tracing::info;
    
    // 初始化 APT 管理器
    let args = OmaAptArgs::builder().build();
    let apt = OmaApt::new(vec![], args, false, AptConfig::new())?;
    
    // 检查安全更新
    info!("Checking for security updates...");
    
    let security_count = apt.count_security_updates()?;
    info!("Found {} security updates", security_count);
    
    if security_count > 0 {
        // 检查是否有关键更新
        if apt.has_critical_security_updates()? {
            info!("🚨 Critical security updates available!");
        }
        
        // 获取按优先级分组的更新
        let grouped_updates = apt.get_security_updates_by_priority()?;
        
        for (priority, updates) in grouped_updates {
            if !updates.is_empty() {
                info!("{:?} priority security updates:", priority);
                for update in updates {
                    info!("  📦 {}: {} → {}", 
                        update.package_name,
                        update.current_version,
                        update.new_version
                    );
                    
                    if update.is_essential {
                        info!("    ⚠️  Essential package");
                    }
                    
                    if let Some(archive) = &update.source_archive {
                        info!("    📍 Source: {}", archive);
                    }
                }
            }
        }
    } else {
        info!("✅ No security updates available");
    }
    
    Ok(())
}
