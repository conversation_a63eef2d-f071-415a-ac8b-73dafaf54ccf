use anyhow::Result;
use oma_pm::{
    apt::{AptConfig, OmaApt, OmaAptArgs},
    pkginfo::AptSource,
};
use oma_utils::dpkg::dpkg_arch;
use std::collections::HashSet;
use tracing::info;

/// 安全更新检测器
pub struct SecurityUpdateDetector {
    apt: OmaApt,
    arch: String,
}

/// 安全更新信息
#[derive(Debug, Clone)]
pub struct SecurityUpdate {
    pub package_name: String,
    pub current_version: String,
    pub new_version: String,
    pub source: String,
    pub is_security: bool,
    pub security_reason: String,
}

impl SecurityUpdateDetector {
    pub fn new() -> Result<Self> {
        let args = OmaAptArgs::builder().build();
        let apt = OmaApt::new(vec![], args, false, AptConfig::new())?;
        let arch = dpkg_arch("/")?;

        Ok(Self { apt, arch })
    }

    /// 获取所有可升级的包，并标识哪些是安全更新
    pub fn get_security_updates(&self) -> Result<Vec<SecurityUpdate>> {
        let mut security_updates = Vec::new();
        
        // 获取所有可升级的包
        let upgradable_packages = self.get_upgradable_packages()?;
        
        for pkg in upgradable_packages {
            let package = self.apt.cache.get(&pkg.package_name);
            if let Some(package) = package {
                if let (Some(installed), Some(candidate)) = (package.installed(), package.candidate()) {
                    let current_version = installed.version().to_string();
                    let new_version = candidate.version().to_string();
                    
                    // 获取包的源信息
                    let sources: Vec<AptSource> = candidate.package_files()
                        .map(AptSource::from)
                        .collect();
                    
                    let (is_security, reason, source_info) = self.is_security_update(&sources);
                    
                    security_updates.push(SecurityUpdate {
                        package_name: pkg.package_name,
                        current_version,
                        new_version,
                        source: source_info,
                        is_security,
                        security_reason: reason,
                    });
                }
            }
        }
        
        Ok(security_updates)
    }

    /// 检查包源是否为安全更新源
    fn is_security_update(&self, sources: &[AptSource]) -> (bool, String, String) {
        for source in sources {
            // 检查是否来自安全更新源
            if let Some(archive) = &source.archive {
                let archive_lower = archive.to_lowercase();
                
                // Debian/Ubuntu 安全更新特征
                if archive_lower.contains("security") {
                    return (true, "Security archive".to_string(), source.to_string());
                }
                
                // Ubuntu 安全更新
                if archive_lower.contains("-security") {
                    return (true, "Ubuntu security updates".to_string(), source.to_string());
                }
                
                // Debian 安全更新
                if archive_lower.contains("stable-security") || 
                   archive_lower.contains("oldstable-security") {
                    return (true, "Debian security updates".to_string(), source.to_string());
                }
            }
            
            // 检查组件
            if let Some(component) = &source.component {
                let component_lower = component.to_lowercase();
                if component_lower.contains("security") {
                    return (true, "Security component".to_string(), source.to_string());
                }
            }
            
            // 检查 URI
            let uri_lower = source.archive_uri.to_lowercase();
            if uri_lower.contains("security") {
                return (true, "Security repository".to_string(), source.to_string());
            }
        }
        
        (false, "Regular update".to_string(), 
         sources.first().map(|s| s.to_string()).unwrap_or_default())
    }

    /// 获取可升级包的基本信息
    fn get_upgradable_packages(&self) -> Result<Vec<UpgradablePackage>> {
        use oma_apt::cache::PackageSort;
        
        let sort = PackageSort::default().upgradable();
        let packages: Vec<UpgradablePackage> = self.apt.cache
            .packages(&sort)
            .map(|pkg| UpgradablePackage {
                package_name: pkg.fullname(true),
            })
            .collect();
        
        Ok(packages)
    }

    /// 只获取安全更新
    pub fn get_only_security_updates(&self) -> Result<Vec<SecurityUpdate>> {
        let all_updates = self.get_security_updates()?;
        Ok(all_updates.into_iter().filter(|update| update.is_security).collect())
    }

    /// 获取安全更新统计
    pub fn get_security_update_stats(&self) -> Result<SecurityUpdateStats> {
        let all_updates = self.get_security_updates()?;
        let security_count = all_updates.iter().filter(|u| u.is_security).count();
        let total_count = all_updates.len();
        
        Ok(SecurityUpdateStats {
            total_updates: total_count,
            security_updates: security_count,
            regular_updates: total_count - security_count,
        })
    }
}

#[derive(Debug)]
struct UpgradablePackage {
    package_name: String,
}

#[derive(Debug)]
pub struct SecurityUpdateStats {
    pub total_updates: usize,
    pub security_updates: usize,
    pub regular_updates: usize,
}

/// 使用示例
#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();
    
    let detector = SecurityUpdateDetector::new()?;
    
    // 获取安全更新统计
    let stats = detector.get_security_update_stats()?;
    info!("Update Statistics:");
    info!("  Total updates: {}", stats.total_updates);
    info!("  Security updates: {}", stats.security_updates);
    info!("  Regular updates: {}", stats.regular_updates);
    
    // 获取所有安全更新
    let security_updates = detector.get_only_security_updates()?;
    
    if security_updates.is_empty() {
        info!("No security updates available.");
    } else {
        info!("Available security updates:");
        for update in &security_updates {
            info!("  📦 {}: {} → {} ({})", 
                update.package_name,
                update.current_version,
                update.new_version,
                update.security_reason
            );
            info!("     Source: {}", update.source);
        }
    }
    
    // 获取所有更新（包括安全和常规）
    let all_updates = detector.get_security_updates()?;
    info!("\nAll available updates:");
    for update in &all_updates {
        let security_marker = if update.is_security { "🔒" } else { "📦" };
        info!("  {} {}: {} → {}", 
            security_marker,
            update.package_name,
            update.current_version,
            update.new_version
        );
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_update_detection() -> Result<()> {
        let detector = SecurityUpdateDetector::new()?;
        let stats = detector.get_security_update_stats()?;
        
        // 基本检查
        assert!(stats.total_updates >= stats.security_updates);
        assert_eq!(stats.total_updates, stats.security_updates + stats.regular_updates);
        
        Ok(())
    }
}
