use anyhow::Result;
use oma_pm::{
    apt::{AptConfig, OmaApt, OmaAptArgs},
    pkginfo::OmaPackage,
    matches::PackagesMatcher,
};
use oma_utils::dpkg::dpkg_arch;
use regex::Regex;
use std::collections::HashMap;
use tracing::{info, warn};

/// 高级安全更新检测器
pub struct AdvancedSecurityDetector {
    apt: OmaApt,
    arch: String,
    security_keywords: Vec<Regex>,
}

/// 安全更新详细信息
#[derive(Debug, Clone)]
pub struct DetailedSecurityUpdate {
    pub package_name: String,
    pub current_version: String,
    pub new_version: String,
    pub security_level: SecurityLevel,
    pub cve_ids: Vec<String>,
    pub description: String,
    pub source_info: String,
    pub urgency: UpdateUrgency,
}

#[derive(Debug, Clone, PartialEq)]
pub enum SecurityLevel {
    Critical,
    High,
    Medium,
    Low,
    Unknown,
}

#[derive(Debug, Clone, PartialEq)]
pub enum UpdateUrgency {
    Emergency,
    High,
    Medium,
    Low,
    Unspecified,
}

impl AdvancedSecurityDetector {
    pub fn new() -> Result<Self> {
        let args = OmaAptArgs::builder().build();
        let apt = OmaApt::new(vec![], args, false, AptConfig::new())?;
        let arch = dpkg_arch("/")?;
        
        // 编译安全相关的正则表达式
        let security_keywords = vec![
            Regex::new(r"(?i)CVE-\d{4}-\d{4,}")?,
            Regex::new(r"(?i)security\s+fix")?,
            Regex::new(r"(?i)vulnerability")?,
            Regex::new(r"(?i)exploit")?,
            Regex::new(r"(?i)buffer\s+overflow")?,
            Regex::new(r"(?i)privilege\s+escalation")?,
            Regex::new(r"(?i)denial\s+of\s+service")?,
            Regex::new(r"(?i)remote\s+code\s+execution")?,
            Regex::new(r"(?i)cross-site\s+scripting")?,
            Regex::new(r"(?i)sql\s+injection")?,
        ];

        Ok(Self {
            apt,
            arch,
            security_keywords,
        })
    }

    /// 获取详细的安全更新信息
    pub fn get_detailed_security_updates(&self) -> Result<Vec<DetailedSecurityUpdate>> {
        let mut security_updates = Vec::new();
        
        // 获取所有可升级的包
        use oma_apt::cache::PackageSort;
        let sort = PackageSort::default().upgradable();
        
        for pkg in self.apt.cache.packages(&sort) {
            if let (Some(installed), Some(candidate)) = (pkg.installed(), pkg.candidate()) {
                let package_name = pkg.fullname(true);
                let current_version = installed.version().to_string();
                let new_version = candidate.version().to_string();
                
                // 分析包的安全性
                let security_analysis = self.analyze_package_security(&pkg, &candidate)?;
                
                if security_analysis.is_security_related {
                    security_updates.push(DetailedSecurityUpdate {
                        package_name,
                        current_version,
                        new_version,
                        security_level: security_analysis.level,
                        cve_ids: security_analysis.cve_ids,
                        description: security_analysis.description,
                        source_info: security_analysis.source_info,
                        urgency: security_analysis.urgency,
                    });
                }
            }
        }
        
        // 按安全级别排序
        security_updates.sort_by(|a, b| {
            self.security_level_priority(&a.security_level)
                .cmp(&self.security_level_priority(&b.security_level))
        });
        
        Ok(security_updates)
    }

    /// 分析包的安全性
    fn analyze_package_security(&self, pkg: &oma_apt::Package, candidate: &oma_apt::Version) -> Result<SecurityAnalysis> {
        let mut analysis = SecurityAnalysis::default();
        
        // 1. 检查包源
        let sources: Vec<_> = candidate.package_files().collect();
        for source in &sources {
            if let Some(archive) = source.archive() {
                if archive.to_lowercase().contains("security") {
                    analysis.is_security_related = true;
                    analysis.level = SecurityLevel::High;
                    analysis.source_info = format!("Security archive: {}", archive);
                    analysis.urgency = UpdateUrgency::High;
                }
            }
        }
        
        // 2. 检查包描述和变更日志
        if let Some(description) = candidate.description() {
            analysis.description = description.clone();
            
            // 搜索安全关键词
            let mut found_cves = Vec::new();
            let mut security_keywords_found = Vec::new();
            
            for regex in &self.security_keywords {
                if let Some(captures) = regex.find(&description) {
                    let matched_text = captures.as_str();
                    
                    if matched_text.starts_with("CVE-") || matched_text.starts_with("cve-") {
                        found_cves.push(matched_text.to_uppercase());
                    } else {
                        security_keywords_found.push(matched_text.to_string());
                    }
                    
                    analysis.is_security_related = true;
                }
            }
            
            analysis.cve_ids = found_cves;
            
            // 根据找到的关键词确定安全级别
            if !analysis.cve_ids.is_empty() {
                analysis.level = self.determine_cve_severity(&analysis.cve_ids);
                analysis.urgency = UpdateUrgency::High;
            } else if !security_keywords_found.is_empty() {
                analysis.level = SecurityLevel::Medium;
                analysis.urgency = UpdateUrgency::Medium;
            }
        }
        
        // 3. 检查包的重要性（essential packages 的安全更新更重要）
        if pkg.is_essential() && analysis.is_security_related {
            analysis.urgency = UpdateUrgency::Emergency;
            if analysis.level == SecurityLevel::Unknown {
                analysis.level = SecurityLevel::High;
            }
        }
        
        Ok(analysis)
    }

    /// 根据 CVE 确定严重程度（简化版本）
    fn determine_cve_severity(&self, cve_ids: &[String]) -> SecurityLevel {
        // 这里可以集成 CVE 数据库查询
        // 目前使用简化的启发式方法
        if cve_ids.len() > 3 {
            SecurityLevel::Critical
        } else if cve_ids.len() > 1 {
            SecurityLevel::High
        } else {
            SecurityLevel::Medium
        }
    }

    /// 安全级别优先级（用于排序）
    fn security_level_priority(&self, level: &SecurityLevel) -> u8 {
        match level {
            SecurityLevel::Critical => 0,
            SecurityLevel::High => 1,
            SecurityLevel::Medium => 2,
            SecurityLevel::Low => 3,
            SecurityLevel::Unknown => 4,
        }
    }

    /// 获取紧急安全更新
    pub fn get_emergency_updates(&self) -> Result<Vec<DetailedSecurityUpdate>> {
        let all_updates = self.get_detailed_security_updates()?;
        Ok(all_updates.into_iter()
            .filter(|update| update.urgency == UpdateUrgency::Emergency || 
                           update.security_level == SecurityLevel::Critical)
            .collect())
    }

    /// 生成安全更新报告
    pub fn generate_security_report(&self) -> Result<SecurityReport> {
        let updates = self.get_detailed_security_updates()?;
        
        let mut report = SecurityReport::default();
        
        for update in &updates {
            match update.security_level {
                SecurityLevel::Critical => report.critical_count += 1,
                SecurityLevel::High => report.high_count += 1,
                SecurityLevel::Medium => report.medium_count += 1,
                SecurityLevel::Low => report.low_count += 1,
                SecurityLevel::Unknown => report.unknown_count += 1,
            }
            
            report.total_cves += update.cve_ids.len();
        }
        
        report.total_security_updates = updates.len();
        report.updates = updates;
        
        Ok(report)
    }
}

#[derive(Debug, Default)]
struct SecurityAnalysis {
    is_security_related: bool,
    level: SecurityLevel,
    cve_ids: Vec<String>,
    description: String,
    source_info: String,
    urgency: UpdateUrgency,
}

impl Default for SecurityLevel {
    fn default() -> Self {
        SecurityLevel::Unknown
    }
}

impl Default for UpdateUrgency {
    fn default() -> Self {
        UpdateUrgency::Unspecified
    }
}

#[derive(Debug, Default)]
pub struct SecurityReport {
    pub total_security_updates: usize,
    pub critical_count: usize,
    pub high_count: usize,
    pub medium_count: usize,
    pub low_count: usize,
    pub unknown_count: usize,
    pub total_cves: usize,
    pub updates: Vec<DetailedSecurityUpdate>,
}

/// 使用示例
#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();
    
    let detector = AdvancedSecurityDetector::new()?;
    
    // 生成安全报告
    let report = detector.generate_security_report()?;
    
    info!("🔒 Security Update Report");
    info!("========================");
    info!("Total security updates: {}", report.total_security_updates);
    info!("Critical: {}", report.critical_count);
    info!("High: {}", report.high_count);
    info!("Medium: {}", report.medium_count);
    info!("Low: {}", report.low_count);
    info!("Total CVEs addressed: {}", report.total_cves);
    
    // 显示紧急更新
    let emergency_updates = detector.get_emergency_updates()?;
    if !emergency_updates.is_empty() {
        warn!("🚨 EMERGENCY SECURITY UPDATES:");
        for update in &emergency_updates {
            warn!("  {} {} → {} ({:?})", 
                update.package_name,
                update.current_version,
                update.new_version,
                update.security_level
            );
            if !update.cve_ids.is_empty() {
                warn!("    CVEs: {}", update.cve_ids.join(", "));
            }
        }
    }
    
    // 显示所有安全更新
    if !report.updates.is_empty() {
        info!("\nAll security updates:");
        for update in &report.updates {
            let level_emoji = match update.security_level {
                SecurityLevel::Critical => "🔴",
                SecurityLevel::High => "🟠",
                SecurityLevel::Medium => "🟡",
                SecurityLevel::Low => "🟢",
                SecurityLevel::Unknown => "⚪",
            };
            
            info!("  {} {} {} → {} ({:?})", 
                level_emoji,
                update.package_name,
                update.current_version,
                update.new_version,
                update.security_level
            );
            
            if !update.cve_ids.is_empty() {
                info!("    CVEs: {}", update.cve_ids.join(", "));
            }
        }
    } else {
        info!("✅ No security updates available.");
    }
    
    Ok(())
}
