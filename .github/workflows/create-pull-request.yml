name: create-pull-request

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  create-abbs-pr:
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set env
        env:
          ACTION_DEPLOY_KEY: ${{ secrets.ACTION_DEPLOY_KEY }}
        run: |
          echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
          mkdir -p ~/.ssh/
          echo "$ACTION_DEPLOY_KEY" | tr -d '\r' > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Get `aosc-os-abbs`
        run: <NAME_EMAIL>:AOSC-Dev/aosc-os-abbs.git
      - name: Install dependencies
        run: |
          sudo apt update && sudo apt install -y git gh
      - name: Update version
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
        run: |
          cd ./aosc-os-abbs/app-admin/oma
          source ./spec
          # Note: Use tilde (~) to denote pre-release, allowing users to upgrade to a final release later.
          export DPKG_VER=$(echo ${RELEASE_VERSION} | sed -e 's|v||g' | sed -e 's|-|~|g')
          sed -e "s|VER=$VER|VER=${DPKG_VER}|g" -i spec
          sed -e "s|REL=$REL||g" -i spec
          sed -e '/^$/d' -i spec
          git config user.name "eatradish"
          git config user.email "<EMAIL>"
          # Note: Git not allow tilde (~) in branch names, so use dash here.
          git checkout -b oma-${DPKG_VER/\~/-}
          git add .
          git commit -m "oma: update to ${DPKG_VER}"
          # Note: Git not allow tilde (~) in branch names, so use dash here.
          git push --set-upstream origin oma-${DPKG_VER/\~/-}
          uri=$(echo -e "Topic Description\n-----------------\n\n- oma: update to ${DPKG_VER}\n\nPackage(s) Affected\n-------------------\n\n- oma: ${DPKG_VER}\n\nSecurity Update?\n----------------\n\nNo\n\nBuild Order\n-----------\n\n\`\`\`\n#buildit oma\n\`\`\`\n\nTest Build(s) Done\n------------------\n\n**Primary Architectures**\n\n- [ ] AMD64 \`amd64\`\n- [ ] AArch64 \`arm64\`\n- [ ] LoongArch 64-bit \`loongarch64\`\n\n**Secondary Architectures**\n\n- [ ] Loongson 3 \`loongson3\`\n- [ ] PowerPC 64-bit (Little Endian) \`ppc64el\`\n- [ ] RISC-V 64-bit \`riscv64\`\n\n\n" | gh pr create -d --title "oma: update to ${DPKG_VER}" -l "upgrade" -F -)
          num=$(basename ${uri})
          curl --header "Content-Type: application/json" \
            --request POST \
            --data "{\"pr\": ${num}}" \
            https://buildit.aosc.io/api/pipeline/new_pr
