name: Rust

on:
  push:
    branches: [ "*" ]
  pull_request:
    branches: [ "master" ]

env:
  CARGO_TERM_COLOR: always

jobs:
  rustfmt:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v3

      - name: Install stable toolchain with rustfmt available
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
          components: rustfmt

      - name: Check format
        run: cargo fmt --check
  clippy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
            toolchain: stable
            components: clippy
            override: true
      - name: Install Dependency
        run: sudo apt-get -y update && sudo apt-get -y install libapt-pkg-dev nettle-dev libssl-dev libclang-dev clang llvm
      - uses: actions-rs/clippy-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Check clippy
        run: cargo clippy -- -D warnings
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
            toolchain: stable
            override: true
      - name: Install Dependency
        run: sudo apt-get -y update && sudo apt-get -y install libapt-pkg-dev nettle-dev libssl-dev libclang-dev clang llvm
      - name: Build
        run: cargo build
      - name: Build with generic feature
        run: cargo build --no-default-features --features generic
      - name: Test
        run: cargo test --workspace
