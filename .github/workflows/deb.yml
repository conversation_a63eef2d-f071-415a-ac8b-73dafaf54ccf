name: deb-build

on:
  push:
    tags: [ "v*" ]

jobs:
  package:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target:
          [
            "ubuntu:20.04",
            "ubuntu:22.04",
            "ubuntu:24.04",
            "debian:11",
            "debian:12",
            "linuxdeepin/deepin",
            "chenchongbiao/openkylin",
          ]
    container:
      image: "${{ matrix.target }}"
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install Dependencies
        shell: bash
        run: |
          apt-get update
          apt-get install -y build-essential zlib1g-dev libssl-dev pkgconf nettle-dev libapt-pkg-dev curl xz-utils clang openssh-client rsync
      - uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          toolchain: stable
          cache: false
      - uses: Swatinem/rust-cache@v2
        with:
          key: "${{ matrix.target }}"
      - name: Install cargo-deb
        run: cargo install -f cargo-deb
      - name: Build deb package
        shell: bash
        run: |
          cargo deb -Z xz
          VER_SUFFIX="${TARGET_DISTRO/:/}"
          VER_SUFFIX="${VER_SUFFIX##*\/}"
          case "${TARGET_DISTRO}" in
              'debian:11')
                  CODENAME='bullseye'
                  ;;
              'debian:12')
                  CODENAME='bookworm'
                  ;;
              'ubuntu:20.04')
                  CODENAME='focal'
                  ;;
              'ubuntu:22.04')
                  CODENAME='jammy'
                  ;;
              'ubuntu:24.04')
                  CODENAME='noble'
                  ;;
              'linuxdeepin/deepin')
                  CODENAME='beige'
                  VER_SUFFIX="${VER_SUFFIX}23"
                  ;;
              'chenchongbiao/openkylin')
                  CODENAME='nile'
                  VER_SUFFIX="${VER_SUFFIX}2.0"
                  ;;
          esac
          for i in target/debian/*.deb; do
            mv -v "${i}" "${i/\.deb/-$VER_SUFFIX.deb}"
          done
          echo "VER_SUFFIX=${VER_SUFFIX}" >> "${GITHUB_ENV}"
          echo "CODENAME=${CODENAME}" >> "${GITHUB_ENV}"
        env:
          TARGET_DISTRO: "${{ matrix.target }}"
      - name: Setup SSH private key
        env:
          KEY: ${{ secrets.KEY }}
        run: |
          mkdir -p ~/.ssh/
          chmod 0700 ~/.ssh/
          echo "$KEY" > ~/.ssh/id_ed25519
          cp .github/workflows/known_hosts ~/.ssh/known_hosts
          chmod 0600 ~/.ssh/id_ed25519 ~/.ssh/known_hosts
      - name: Upload packages (repository)
        shell: bash
        run: |
          rsync \
              --ignore-existing \
              -v \
              -e "ssh \
                  -o IdentityFile=$HOME/.ssh/id_ed25519 \
                  -o UserKnownHostsFile=$HOME/.ssh/known_hosts" \
              target/debian/*-"${VER_SUFFIX}".deb \
              ${USER}@repo.aosc.io:/mirror/oma/pool/"${CODENAME}"/main/o/
          ssh \
              -v \
              -o IdentityFile=~/.ssh/id_ed25519 \
              -o UserKnownHostsFile=~/.ssh/known_hosts \
              ${USER}@repo.aosc.io \
              touch /mirror/.updated
        env:
          TARGET_DISTRO: "${{ matrix.target }}"
          USER: ${{ secrets.USER }}
      - name: Upload packages (GitHub Release)
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.VER_SUFFIX }}
          path: "target/debian/*.deb"
  # Release stage
  release:
    runs-on: ubuntu-latest
    needs: package
    if: ${{ startsWith(github.ref, 'refs/tags/') }}
    steps:
      - uses: actions/download-artifact@v4
      - name: Upload artifacts
        uses: alexellis/upload-assets@0.4.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          asset_paths: '["./**/*.tar.*","./**/*.deb"]'
