name: Publish Release
on:
  push:
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Generate a release body
        uses: orhun/git-cliff-action@main
        id: git-cliff
        with:
          config: cliff.toml
          args: -vv --latest --strip header --no-exec --github-repo ${{ github.repository }}
        env:
          OUTPUT: CHANGELOG.md
      - name: Print the changelog
        run: cat "${{ steps.git-cliff.outputs.changelog }}"
      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          body: ${{ steps.git-cliff.outputs.content }}
