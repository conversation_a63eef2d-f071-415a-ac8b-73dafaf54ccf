[package]
name = "oma-refresh"
version = "0.35.5"
edition = "2021"
description = "APT repository refresh handler library"
license = "GPL-3.0-or-later"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
oma-fetch = { version = "^0.19.0", path = "../oma-fetch", default-features = false }
thiserror = "2"
url = "2.3"
tokio = { version = "1.28", default-features = false, features = ["fs", "process"] }
futures = "0.3"
oma-apt-sources-lists = "0.5"
oma-debcontrol = "0.3"
sequoia-openpgp = { version = "1.20", default-features = false }
anyhow = "1.0"
chrono = { version = "0.4", features = ["unstable-locales"] }
oma-topics = { version = "^0.18.0", path = "../oma-topics", optional = true, default-features = false }
oma-utils = { version = "^0.10.0", path = "../oma-utils", features = ["dpkg"] }
tracing ="0.1"
smallvec = "1.1"
oma-repo-verify = { version = "^0.1.0", path = "../oma-repo-verify", default-features = false }
ahash = "0.8.11"
oma-apt = "0.8.0"
aho-corasick = "1.1.3"
# https://github.com/bytecodealliance/rustix/pull/1077
# rustix = { version = "0.38", features = ["fs"] }
nix = { version = "0.29", features = ["fs"] }
sysinfo = "0.32"
bon = "3"
once_cell = "1.19"
apt-auth-config = { version = "0.2.0", path = "../apt-auth-config" }

[features]
aosc = ["dep:oma-topics"]
sequoia-openssl-backend = ["oma-repo-verify/sequoia-openssl-backend"]
sequoia-nettle-backend = ["oma-repo-verify/sequoia-nettle-backend"]
rustls = ["oma-fetch/rustls", "oma-topics/rustls"]
native-tls = ["oma-fetch/native-tls", "oma-topics/native-tls"]
default = ["aosc", "sequoia-nettle-backend", "rustls"]

[dev-dependencies]
tokio = { version = "1.28", default-features = false, features = ["macros", "rt-multi-thread"] }
indicatif = "0.17"
dashmap = "6"
oma-console = { path = "../oma-console" }
