[package]
name = "oma-fetch"
version = "0.19.0"
edition = "2021"
description = "APT repository download routines library"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
thiserror = "2"
reqwest = { version = "0.12", default-features = false, features = ["stream"] }
tokio = { version = "1.28", default-features = false, features = ["fs"] }
serde = { version = "1.0", features = ["derive"] }
faster-hex = "0.10"
sha2 = "0.10"
futures = "0.3"
async-compression = { version = "0.4", features = ["gzip", "xz", "futures-io", "bzip2", "zstd"] }
oma-utils = { version = "^0.10", features = ["url-no-escape"] }
tracing = "0.1"
tokio-util = { version = "0.7", features = ["compat"] }
md-5 = "0.10.6"
bon = "3"

[dev-dependencies]
tokio = { version = "1.28", default-features = false, features = ["macros", "rt-multi-thread"] }
indicatif = "0.17"
dashmap = "6"
oma-console = { path = "../oma-console" }

[features]
rustls = ["reqwest/rustls-tls"]
native-tls = ["reqwest/native-tls"]
default = ["rustls"]
