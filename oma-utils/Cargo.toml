[package]
name = "oma-utils"
version = "0.10.2"
edition = "2021"
description = "General system API and utilities used by oma"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
thiserror = "2"
number_prefix = { version = "0.4", optional = true }
os-release = "0.1"
oma-console = { version = "^0.23", path = "../oma-console", optional = true, default-features = false, features = [
    "print",
] }
zbus = { version = "4.1", optional = true }
url-escape = { version = "0.1", optional = true }
tracing = "0.1"
logind-zbus = "4.0.3"

[features]
dpkg = []
human-bytes = ["number_prefix"]
dbus = ["zbus"]
oma = ["dep:oma-console"]
url-no-escape = ["url-escape"]
