.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-upgrade 1  "upgrade " 
.SH NAME
upgrade \- Upgrade packages installed on the system
.SH SYNOPSIS
\fBupgrade\fR [\fB\-y\fR|\fB\-\-yes\fR] [\fB\-\-force\-unsafe\-io\fR] [\fB\-\-force\-yes\fR] [\fB\-\-force\-confnew\fR] [\fB\-\-dry\-run\fR] [\fB\-\-autoremove\fR] [\fB\-\-remove\-config\fR] [\fB\-\-no\-refresh\-topics\fR] [\fB\-h\fR|\fB\-\-help\fR] [\fIpackages\fR] 
.SH DESCRIPTION
Upgrade packages installed on the system
.SH OPTIONS
.TP
\fB\-y\fR, \fB\-\-yes\fR
Bypass confirmation prompts
.TP
\fB\-\-force\-unsafe\-io\fR
Install package(s) without fsync(2)
.TP
\fB\-\-force\-yes\fR
Ignore repository and package dependency issues
.TP
\fB\-\-force\-confnew\fR
Replace configuration file(s) in the system those shipped in the package(s) to be installed (invokes `dpkg \-\-force\-confnew`)
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-\-autoremove\fR
Auto remove unnecessary package(s)
.TP
\fB\-\-remove\-config\fR
Remove package(s) also remove configuration file(s), like apt purge
.TP
\fB\-\-no\-refresh\-topics\fR
Do not refresh topics manifest.json file
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
.TP
[\fIpackages\fR]
Package(s) to upgrade
