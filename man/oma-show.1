.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-show 1  "show " 
.SH NAME
show \- Show information on the specified package(s)
.SH SYNOPSIS
\fBshow\fR [\fB\-a\fR|\fB\-\-all\fR] [\fB\-\-json\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIpackages\fR> 
.SH DESCRIPTION
Show information on the specified package(s)
.SH OPTIONS
.TP
\fB\-a\fR, \fB\-\-all\fR
Show information on all available version(s) of (a) package(s) from all repository(ies)
.TP
\fB\-\-json\fR
Set output format as JSON
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.TP
<\fIpackages\fR>

