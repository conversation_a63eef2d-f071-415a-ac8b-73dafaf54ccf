.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-remove 1  "remove " 
.SH NAME
remove \- Remove the specified package(s)
.SH SYNOPSIS
\fBremove\fR [\fB\-y\fR|\fB\-\-yes\fR] [\fB\-\-force\-unsafe\-io\fR] [\fB\-\-force\-yes\fR] [\fB\-\-no\-autoremove\fR] [\fB\-f\fR|\fB\-\-fix\-broken\fR] [\fB\-\-remove\-config\fR] [\fB\-\-dry\-run\fR] [\fB\-h\fR|\fB\-\-help\fR] [\fIpackages\fR] 
.SH DESCRIPTION
Remove the specified package(s)
.SH OPTIONS
.TP
\fB\-y\fR, \fB\-\-yes\fR
Bypass confirmation prompts
.TP
\fB\-\-force\-unsafe\-io\fR
Install package(s) without fsync(2)
.TP
\fB\-\-force\-yes\fR
Ignore repository and package dependency issues
.TP
\fB\-\-no\-autoremove\fR
Do not remove package(s) without reverse dependencies
.TP
\fB\-f\fR, \fB\-\-fix\-broken\fR
Fix apt broken status
.TP
\fB\-\-remove\-config\fR
Remove package(s) also remove configuration file(s), like apt purge
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
.TP
[\fIpackages\fR]
Package(s) to remove
