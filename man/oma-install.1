.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-install 1  "install " 
.SH NAME
install \- Install package(s) from the repository
.SH SYNOPSIS
\fBinstall\fR [\fB\-\-install\-dbg\fR] [\fB\-\-reinstall\fR] [\fB\-\-install\-recommends\fR] [\fB\-\-install\-suggests\fR] [\fB\-\-no\-install\-recommends\fR] [\fB\-\-no\-install\-suggests\fR] [\fB\-f\fR|\fB\-\-fix\-broken\fR] [\fB\-\-force\-unsafe\-io\fR] [\fB\-\-no\-refresh\fR] [\fB\-y\fR|\fB\-\-yes\fR] [\fB\-\-force\-yes\fR] [\fB\-\-force\-confnew\fR] [\fB\-\-remove\-config\fR] [\fB\-\-dry\-run\fR] [\fB\-\-no\-refresh\-topics\fR] [\fB\-h\fR|\fB\-\-help\fR] [\fIpackages\fR] 
.SH DESCRIPTION
Install package(s) from the repository
.SH OPTIONS
.TP
\fB\-\-install\-dbg\fR
Install debug symbols for (a) package(s)
.TP
\fB\-\-reinstall\fR
Reinstall package(s) by downloading a current copy from the repository
.TP
\fB\-\-install\-recommends\fR
Install recommended packages(s)
.TP
\fB\-\-install\-suggests\fR
Install suggested package(s)
.TP
\fB\-\-no\-install\-recommends\fR
Do not install recommend package(s)
.TP
\fB\-\-no\-install\-suggests\fR
Do not install recommend package(s)
.TP
\fB\-f\fR, \fB\-\-fix\-broken\fR
Fix apt broken status
.TP
\fB\-\-force\-unsafe\-io\fR
Install package(s) without fsync(2)
.TP
\fB\-\-no\-refresh\fR
Do not refresh repository metadata
.TP
\fB\-y\fR, \fB\-\-yes\fR
Bypass confirmation prompts
.TP
\fB\-\-force\-yes\fR
Ignore repository and package dependency issues
.TP
\fB\-\-force\-confnew\fR
Replace configuration file(s) in the system those shipped in the package(s) to be installed (invokes `dpkg \-\-force\-confnew`)
.TP
\fB\-\-remove\-config\fR
Remove package(s) also remove configuration file(s), like apt purge
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-\-no\-refresh\-topics\fR
Do not refresh topics manifest.json file
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
.TP
[\fIpackages\fR]
Package(s) to install
