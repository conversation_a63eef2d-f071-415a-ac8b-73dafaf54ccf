.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-pick 1  "pick " 
.SH NAME
pick \- Install specific version of a package
.SH SYNOPSIS
\fBpick\fR [\fB\-\-no\-refresh\fR] [\fB\-\-dry\-run\fR] [\fB\-\-no\-refresh\-topics\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIpackage\fR> 
.SH DESCRIPTION
Install specific version of a package
.SH OPTIONS
.TP
\fB\-\-no\-refresh\fR
Do not refresh repository metadata
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-\-no\-refresh\-topics\fR
Do not refresh topics manifest.json file
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
.TP
<\fIpackage\fR>
Package to pick specific version for
