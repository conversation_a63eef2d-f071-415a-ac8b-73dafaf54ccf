.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-fix-broken 1  "fix-broken " 
.SH NAME
fix\-broken \- Resolve broken system dependencies in the system
.SH SYNOPSIS
\fBfix\-broken\fR [\fB\-\-dry\-run\fR] [\fB\-h\fR|\fB\-\-help\fR] 
.SH DESCRIPTION
Resolve broken system dependencies in the system
.SH OPTIONS
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
