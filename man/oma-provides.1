.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-provides 1  "provides " 
.SH NAME
provides \- Search for package(s) that provide(s) certain patterns in a path
.SH SYNOPSIS
\fBprovides\fR [\fB\-\-no\-pager\fR] [\fB\-\-bin\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIpattern\fR> 
.SH DESCRIPTION
Search for package(s) that provide(s) certain patterns in a path
.SH OPTIONS
.TP
\fB\-\-no\-pager\fR
Set output mode as current println mode
.TP
\fB\-\-bin\fR
Search binary of package(s)
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.TP
<\fIpattern\fR>
Keywords, parts of a path, executable names to search
