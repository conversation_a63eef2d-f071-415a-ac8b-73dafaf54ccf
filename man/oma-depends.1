.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-depends 1  "depends " 
.SH NAME
depends \- Lists dependencies of one or multiple packages
.SH SYNOPSIS
\fBdepends\fR [\fB\-\-json\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIpackages\fR> 
.SH DESCRIPTION
Lists dependencies of one or multiple packages
.SH OPTIONS
.TP
\fB\-\-json\fR
Set output format as JSON
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.TP
<\fIpackages\fR>
Package(s) to query dependency(ies) for
