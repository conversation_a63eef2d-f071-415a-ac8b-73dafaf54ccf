.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-mirror 1  "mirror " 
.SH NAME
mirror \- Manage Mirrors enrollment
.SH SYNOPSIS
\fBmirror\fR [\fB\-\-no\-refresh\-topics\fR] [\fB\-\-no\-refresh\fR] [\fB\-h\fR|\fB\-\-help\fR] [\fIsubcommands\fR]
.SH DESCRIPTION
Manage Mirrors enrollment
.SH OPTIONS
.TP
\fB\-\-no\-refresh\-topics\fR
Do not refresh topics manifest.json file
.TP
\fB\-\-no\-refresh\fR
Do not refresh repository metadata
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.SH SUBCOMMANDS
.TP
mirror\-set(1)
Set mirror(s) to sources.list
.TP
mirror\-add(1)
Add mirror(s) to sources.list
.TP
mirror\-remove(1)
Remove mirror(s) from sources.list
.TP
mirror\-sort\-mirrors(1)
Sort mirror(s) order
.TP
mirror\-speedtest(1)
Speedtest mirror(s)
.TP
mirror\-help(1)
Print this message or the help of the given subcommand(s)
