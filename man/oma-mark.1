.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-mark 1  "mark " 
.SH NAME
mark \- Mark status for one or multiple package(s)
.SH SYNOPSIS
\fBmark\fR [\fB\-\-dry\-run\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIaction\fR> <\fIpackages\fR> 
.SH DESCRIPTION
Mark status for one or multiple package(s), oma will resolve dependencies in accordance with the marked status(es) of the specified package(s)
.SH OPTIONS
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
.TP
<\fIaction\fR>

.br
\fIPossible values:\fR
.RS 14
.IP \(bu 2
hold: Lock package version(s), this will prevent the specified package(s) from being updated or downgraded
.IP \(bu 2
unhold: Unlock package version(s), this will undo the “hold” status on the specified package(s)
.IP \(bu 2
manual: Mark package(s) as manually installed, this will prevent the specified package(s) from being removed when all reverse dependencies were removed
.IP \(bu 2
auto: Mark package(s) as automatically installed, this will mark the specified package(s) for removal when all reverse dependencies were removed
.RE
.TP
<\fIpackages\fR>
Package(s) to mark status for
