.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-rdepends 1  "rdepends " 
.SH NAME
rdepends \- List reverse dependency(ies) for the specified package(s)
.SH SYNOPSIS
\fBrdepends\fR [\fB\-\-json\fR] [\fB\-h\fR|\fB\-\-help\fR] <\fIpackages\fR> 
.SH DESCRIPTION
List reverse dependency(ies) for the specified package(s)
.SH OPTIONS
.TP
\fB\-\-json\fR
Set output format as JSON
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.TP
<\fIpackages\fR>
Package(s) to query dependency(ies) for
