.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-list 1  "list " 
.SH NAME
list \- List package(s) available from the repository
.SH SYNOPSIS
\fBlist\fR [\fB\-a\fR|\fB\-\-all\fR] [\fB\-i\fR|\fB\-\-installed\fR] [\fB\-u\fR|\fB\-\-upgradable\fR] [\fB\-m\fR|\fB\-\-manually\-installed\fR] [\fB\-\-automatic\fR] [\fB\-\-autoremovable\fR] [\fB\-\-json\fR] [\fB\-h\fR|\fB\-\-help\fR] [\fIpackages\fR] 
.SH DESCRIPTION
List package(s) available from the repository
.SH OPTIONS
.TP
\fB\-a\fR, \fB\-\-all\fR
List all available version(s) of (a) package(s) from all repository(ies)
.TP
\fB\-i\fR, \fB\-\-installed\fR
List only package(s) currently installed on the system
.TP
\fB\-u\fR, \fB\-\-upgradable\fR
List only package(s) with update(s) available
.TP
\fB\-m\fR, \fB\-\-manually\-installed\fR
List only package(s) with manually installed
.TP
\fB\-\-automatic\fR
List only package(s) with automatic installed
.TP
\fB\-\-autoremovable\fR
List only package(s) with autoremovable
.TP
\fB\-\-json\fR
Set output format as JSON
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help
.TP
[\fIpackages\fR]
Package(s) to list
