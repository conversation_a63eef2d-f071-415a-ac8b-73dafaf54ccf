.ie \n(.g .ds Aq \(aq
.el .ds Aq '
.TH oma-topics 1  "topics " 
.SH NAME
topics \- Manage testing topics enrollment
.SH SYNOPSIS
\fBtopics\fR [\fB\-\-opt\-in\fR] [\fB\-\-opt\-out\fR] [\fB\-\-dry\-run\fR] [\fB\-h\fR|\fB\-\-help\fR] 
.SH DESCRIPTION
Manage testing topics enrollment
.SH OPTIONS
.TP
\fB\-\-opt\-in\fR
Enroll in one or more topic(s), delimited by space
.TP
\fB\-\-opt\-out\fR
Withdraw from one or more topic(s) and rollback to stable versions, delimited by space
.TP
\fB\-\-dry\-run\fR
Run oma in “dry\-run” mode. Useful for testing changes and operations without making changes to the system
.TP
\fB\-h\fR, \fB\-\-help\fR
Print help (see a summary with \*(Aq\-h\*(Aq)
