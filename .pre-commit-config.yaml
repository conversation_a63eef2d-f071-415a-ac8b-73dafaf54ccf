# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
# some configs are from https://github.com/rust-lang/cargo/issues/13758

repos:
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v5.0.0
      hooks:
          - id: trailing-whitespace
          - id: end-of-file-fixer
          - id: check-yaml
          - id: check-added-large-files

    - repo: local
      hooks:
          - id: rustfmt
            name: Rust fmt
            description: Check if all files follow the rustfmt style
            entry: cargo fmt --all -- --check --color always
            language: system
            pass_filenames: false

    - repo: local
      hooks:
          - id: rust-linting
            name: Rust linting
            description: Run cargo fmt on files included in the commit. rustfmt should be installed before-hand.
            entry: cargo fmt --all --
            pass_filenames: true
            types: [file, rust]
            language: system

          - id: rust-clippy
            name: Rust clippy
            description: Run cargo clippy on files included in the commit. clippy should be installed before-hand.
            entry: cargo clippy --examples -- -D warnings
            # strict check uncomment this
            # entry: cargo clippy --all-targets --all-features -- -Dclippy::all
            pass_filenames: false
            types: [file, rust]
            language: system

    - repo: https://github.com/crate-ci/typos
      rev: typos-dict-v0.11.35
      hooks:
          - id: typos
            name: Typo Check
            description: Run typos to check typo.
