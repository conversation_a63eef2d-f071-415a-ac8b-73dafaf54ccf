## To make sure that apt update doesn't delete the contents downloaded by oma refresh
## let apt download Contents and BinContents as well.

Acquire::IndexTargets {
    deb::Contents-deb  {
        MetaKey "$(COMPONENT)/Contents-$(ARCHITECTURE)";
        ShortDescription "Contents-$(ARCHITECTURE)";
        Description "$(RELEASE)/$(COMPONENT) $(ARCHITECTURE) Contents (deb)";

        flatMetaKey "Contents-$(ARCHITECTURE)";
        flatDescription "$(RELEASE) Contents (deb)";
        PDiffs "true";
        KeepCompressed "true";
    };

    deb::BinContents-deb  {
        MetaKey "$(COMPONENT)/BinContents-$(ARCHITECTURE)";
        ShortDescription "BinContents-$(ARCHITECTURE)";
        Description "$(RELEASE)/$(COMPONENT) $(ARCHITECTURE) BinContents (deb)";

        flatMetaKey "BinContents-$(ARCHITECTURE)";
        flatDescription "$(RELEASE) BinContents (deb)";
        PDiffs "true";
        KeepCompressed "false";
    };

    # Download Contents for source files if there is a deb-src
    # line
    deb-src::Contents-dsc  {
        MetaKey "$(COMPONENT)/Contents-source";
        ShortDescription "Contents-source";
        Description "$(RELEASE)/$(COMPONENT) source Contents (dsc)";

        flatMetaKey "Contents-source";
        flatDescription "$(RELEASE) Contents (dsc)";
        PDiffs "true";
        KeepCompressed "true";
        DefaultEnabled "false";
    };

    # Configuration for downloading Contents files for
    # debian-installer packages (udebs).
    deb::Contents-udeb  {
        MetaKey "$(COMPONENT)/Contents-udeb-$(ARCHITECTURE)";
        ShortDescription "Contents-udeb-$(ARCHITECTURE)";
        Description "$(RELEASE)/$(COMPONENT) $(ARCHITECTURE) Contents (udeb)";

        flatMetaKey "Contents-udeb-$(ARCHITECTURE)";
        flatDescription "$(RELEASE) Contents (udeb)";
        KeepCompressed "true";
        PDiffs "true";
        DefaultEnabled "false";
    };

    ### FALLBACKS
    deb::Contents-deb-legacy {
        MetaKey "Contents-$(ARCHITECTURE)";
        ShortDescription "Contents-$(ARCHITECTURE)";
        Description "$(RELEASE) $(ARCHITECTURE) Contents (deb)";

        PDiffs "true";
        KeepCompressed "true";
        Fallback-Of "Contents-deb";
        Identifier "Contents-deb";
    }
}
