<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD polkit Policy Configuration 1.0//EN"
"http://www.freedesktop.org/software/polkit/policyconfig-1.dtd">
<policyconfig>

  <vendor>Anthon Open Source Community</vendor>
  <vendor_url>https://github.com/AOSC-Dev/oma</vendor_url>

  <action id="io.aosc.oma.apply.run">
    <description>Apply package changes</description>
    <description xml:lang="zh_CN">应用软件包修改</description>
    <message>Authentication is required to apply changes to system packages</message>
    <message xml:lang="zh_CN">应用系统软件包修改需要授权</message>
    <icon_name>preferences-system</icon_name>
    <defaults>
      <allow_any>no</allow_any>
      <allow_inactive>no</allow_inactive>
      <allow_active>auth_admin_keep</allow_active>
    </defaults>
    <annotate key="org.freedesktop.policykit.exec.path">/bin/oma</annotate>
  </action>

</policyconfig>
