[package]
name = "oma-pm-operation-type"
version = "0.7.0"
edition = "2021"
license = "MIT"
description = "APT package management operation abstraction library"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
serde = { version = "1.0", features = ["derive"] }
oma-utils = { version = "^0.10.0", path = "../oma-utils", features = [
    "human-bytes",
] }
bon = "3"
