[package]
name = "oma"
version = "1.12.9"
edition = "2021"
description = "User-friendly and performant package manager for APT repositories"
license = "GPL-3.0-or-later"
authors = ["eatradish <<EMAIL>>"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# Cli
clap = { version = "4.5.19", features = ["cargo", "wrap_help", "color", "string"] }
anyhow = "1.0.89"
ctrlc = "3.4.5"
# https://github.com/zhiburt/tabled/issues/423
tabled = { version = "=0.15.0", features = ["ansi"] }
dialoguer = "0.11.0"
tokio = "1.40.0"
oma-inquire = "0.1.1"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
toml = "0.8.19"
dashmap = "6.1.0"
chrono = "0.4.38"
rustix = { version = "0.38.37", features = ["process"] }
colored = { version = "2.1.0", optional = true }
image = { version = "0.25.2", optional = true }
libc = "0.2.159"
reqwest = { version = "0.12.8", default-features = false }
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
smallvec = "1.13.2"
console-subscriber = { version = "0.4.0", optional = true }
ratatui = "0.29.0"
crossterm = "0.28.1"
ansi-to-tui = "7.0.0"
glob-match = "0.2.1"
which = "7"
strsim = "0.11.1"
ahash = "0.8.11"
indexmap = "2.6.0"
faster-hex = "0.10"
sha2 = "0.10"

# oma crates
oma-utils = { path = "./oma-utils", features = ["dbus", "human-bytes", "oma"] }
oma-console = { path = "./oma-console" }
oma-pm = { path = "./oma-pm" }
oma-refresh = { path = "./oma-refresh", default-features = false }
oma-contents = { path = "./oma-contents" }
oma-fetch = { path = "./oma-fetch", default-features = false }
oma-topics = { path = "./oma-topics", optional = true, default-features = false }
oma-history = { path = "./oma-history" }
oma-repo-verify = { path = "./oma-repo-verify", default-features = false }
oma-mirror = { path = "./oma-mirror", optional = true }
apt-auth-config = { path = "./apt-auth-config" }

# i18n
i18n-embed = { version = "0.15.0", features = ["fluent-system", "desktop-requester"]}
i18n-embed-fl = "0.9.1"
rust-embed = "8.5.0"
unic-langid = "0.9.5"

[build-dependencies]
clap_mangen = "0.2.23"
clap_complete = "4.5.29"
clap = { version = "4.5.19", features = ["cargo", "wrap_help", "color", "string"] }
anyhow = "1.0.89"

[features]
aosc = ["dep:oma-topics", "dep:oma-mirror", "oma-refresh/aosc", "oma-pm/aosc", "oma-contents/aosc", "reqwest/blocking"]
sequoia-openssl-backend = ["oma-refresh/sequoia-openssl-backend"]
sequoia-nettle-backend = ["oma-refresh/sequoia-nettle-backend"]
egg = ["dep:colored", "dep:image"]
tokio-console = ["dep:console-subscriber"]
rustls = ["reqwest/rustls-tls", "oma-fetch/rustls", "oma-refresh/rustls", "oma-topics/rustls", "oma-repo-verify/sequoia-nettle-backend"]
openssl = ["reqwest/native-tls", "oma-fetch/native-tls", "oma-refresh/native-tls", "oma-topics/native-tls", "oma-repo-verify/sequoia-openssl-backend"]
generic = ["sequoia-nettle-backend", "rustls"]
default = ["aosc", "generic"]

[workspace]
members = ["oma-contents", "oma-console", "oma-topics", "oma-fetch", "oma-refresh", "oma-utils", "oma-pm", "oma-history", "oma-pm-operation-type", "oma-repo-verify", "oma-mirror", "apt-auth-config"]

[package.metadata.deb]
copyright = "2024, AOSC Dev <<EMAIL>>"
maintainer = "eatradish <<EMAIL>>"
license-file = ["COPYING", "4"]
extended-description = "User-friendly and performant package manager for APT repositories"
depends = "$auto"
section = "admin"
priority = "optional"
assets = [
    ["target/release/oma", "usr/bin/", "755"],
    ["README.md", "usr/share/doc/oma/README", "644"],
    ["data/config/oma-debian.toml", "etc/oma.toml", "644"],
    ["data/apt.conf.d/50oma.conf", "etc/apt/apt.conf.d/50oma.conf", "644"],
    ["data/dbus/oma-dbus.conf", "usr/share/dbus-1/system.d/oma-dbus.conf", "644"],
    ["data/policykit/io.aosc.oma.apply.policy", "usr/share/polkit-1/actions/io.aosc.oma.apply.policy", "644" ],
    ["data/completions/oma.bash", "usr/share/bash-completion/completions/oma.bash", "644"],
    ["data/completions/oma.fish", "usr/share/fish/vendor_completions.d/oma.fish", "644"],
    ["man/*.1", "usr/share/man/man1/", "644"]
]
features = ["generic"]
default-features = false

[profile.release]
lto = "thin"
opt-level = 3
codegen-units = 1
